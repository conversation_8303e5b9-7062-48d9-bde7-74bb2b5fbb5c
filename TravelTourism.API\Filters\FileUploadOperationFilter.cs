using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace TravelTourism.API.Filters;

public class FileUploadOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var fileParameters = context.MethodInfo.GetParameters()
            .Where(p => p.ParameterType == typeof(IFormFile) || 
                       p.ParameterType == typeof(IFormFile[]) ||
                       p.ParameterType == typeof(List<IFormFile>) ||
                       p.ParameterType == typeof(IList<IFormFile>) ||
                       p.ParameterType == typeof(ICollection<IFormFile>) ||
                       p.ParameterType == typeof(IEnumerable<IFormFile>))
            .ToArray();

        if (fileParameters.Length == 0)
            return;

        operation.RequestBody = new OpenApiRequestBody
        {
            Content = new Dictionary<string, OpenApiMediaType>
            {
                ["multipart/form-data"] = new OpenApiMediaType
                {
                    Schema = new OpenApiSchema
                    {
                        Type = "object",
                        Properties = new Dictionary<string, OpenApiSchema>(),
                        Required = new HashSet<string>()
                    }
                }
            }
        };

        var schema = operation.RequestBody.Content["multipart/form-data"].Schema;

        // Add file parameters
        foreach (var fileParam in fileParameters)
        {
            var isArray = fileParam.ParameterType != typeof(IFormFile);
            var fileSchema = new OpenApiSchema
            {
                Type = "string",
                Format = "binary"
            };

            if (isArray)
            {
                fileSchema = new OpenApiSchema
                {
                    Type = "array",
                    Items = new OpenApiSchema
                    {
                        Type = "string",
                        Format = "binary"
                    }
                };
            }

            schema.Properties.Add(fileParam.Name!, fileSchema);

            // Check if parameter is required (not nullable)
            if (!IsNullable(fileParam))
            {
                schema.Required.Add(fileParam.Name!);
            }
        }

        // Add other form parameters
        var otherFormParameters = context.MethodInfo.GetParameters()
            .Where(p => p.GetCustomAttribute<Microsoft.AspNetCore.Mvc.FromFormAttribute>() != null &&
                       p.ParameterType != typeof(IFormFile) &&
                       !IsFileCollectionType(p.ParameterType))
            .ToArray();

        foreach (var param in otherFormParameters)
        {
            var paramSchema = new OpenApiSchema
            {
                Type = GetOpenApiType(param.ParameterType)
            };

            schema.Properties.Add(param.Name!, paramSchema);

            // Check if parameter is required (not nullable)
            if (!IsNullable(param))
            {
                schema.Required.Add(param.Name!);
            }
        }

        // Remove parameters that are now part of the request body
        var parametersToRemove = operation.Parameters
            .Where(p => fileParameters.Any(fp => fp.Name == p.Name) ||
                       otherFormParameters.Any(ofp => ofp.Name == p.Name))
            .ToList();

        foreach (var param in parametersToRemove)
        {
            operation.Parameters.Remove(param);
        }
    }

    private static bool IsFileCollectionType(Type type)
    {
        return type == typeof(IFormFile[]) ||
               type == typeof(List<IFormFile>) ||
               type == typeof(IList<IFormFile>) ||
               type == typeof(ICollection<IFormFile>) ||
               type == typeof(IEnumerable<IFormFile>);
    }

    private static bool IsNullable(ParameterInfo parameter)
    {
        // Check if the parameter type is nullable
        if (parameter.ParameterType.IsGenericType &&
            parameter.ParameterType.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            return true;
        }

        // Check if the parameter has a default value or is marked as optional
        if (parameter.HasDefaultValue || parameter.IsOptional)
        {
            return true;
        }

        // For reference types, check nullable reference type annotations
        var nullableContext = new NullabilityInfoContext();
        var nullabilityInfo = nullableContext.Create(parameter);
        return nullabilityInfo.WriteState == NullabilityState.Nullable;
    }

    private static string GetOpenApiType(Type type)
    {
        if (type == typeof(string))
            return "string";
        if (type == typeof(int) || type == typeof(int?))
            return "integer";
        if (type == typeof(long) || type == typeof(long?))
            return "integer";
        if (type == typeof(float) || type == typeof(float?) ||
            type == typeof(double) || type == typeof(double?) ||
            type == typeof(decimal) || type == typeof(decimal?))
            return "number";
        if (type == typeof(bool) || type == typeof(bool?))
            return "boolean";
        if (type == typeof(DateTime) || type == typeof(DateTime?))
            return "string";

        return "string"; // Default fallback
    }
}
