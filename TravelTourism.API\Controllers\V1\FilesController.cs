using Microsoft.AspNetCore.Mvc;
using TravelTourism.Application.DTOs.Common;
using TravelTourism.Application.Interfaces;
using TravelTourism.API.Controllers.Base;
using TravelTourism.Application.Models.Requests;
using TravelTourism.API.Models.Requests;

namespace TravelTourism.API.Controllers.V1;

[ApiController]
[Route("api/v1/[controller]")]
public class FilesController : BaseController
{
    private readonly IFileService _fileService;

    public FilesController(IFileService fileService)
    {
        _fileService = fileService;
    }

    /// <summary>
    /// Upload an image file
    /// </summary>
    /// <param name="uploadDto">The file upload data</param>
    /// <returns>The URL of the uploaded image</returns>
    [HttpPost("upload-image")]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    public async Task<IActionResult> UploadImage([FromForm] FileUploadFormDto uploadDto)
    {
        var request = new TravelTourism.Application.Models.Requests.FileUploadRequest { File = uploadDto.File, Folder = uploadDto.Folder };
        var result = await _fileService.UploadImageAsync(request);

        if (!result.Success)
        {
            return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
        }

        return Ok(CreateSuccessResponse(new { ImageUrl = result.Data }, "Image uploaded successfully"));
    }

    /// <summary>
    /// Upload a document file
    /// </summary>
    /// <param name="uploadDto">The file upload data</param>
    /// <returns>The URL of the uploaded document</returns>
    [HttpPost("upload-document")]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(typeof(ApiResponse<object>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    public async Task<IActionResult> UploadDocument([FromForm] FileUploadFormDto uploadDto)
    {
        var request = new TravelTourism.Application.Models.Requests.FileUploadRequest { File = uploadDto.File, Folder = uploadDto.Folder };
        var result = await _fileService.UploadDocumentAsync(request);

        if (!result.Success)
        {
            return BadRequest(CreateErrorResponse(result.Message, result.ErrorCode));
        }

        return Ok(CreateSuccessResponse(new { DocumentUrl = result.Data }, "Document uploaded successfully"));
    }
} 