import { Component, inject, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { CommonModule } from '@angular/common';
import { StateService } from './core/services/state.service';
import { AuthService } from './core/services/auth.service';
import { NotificationService } from './core/services/notification.service';
import { HeaderComponent } from './shared/components/header/header.component';
import { FooterComponent } from './shared/components/footer/footer.component';
import { NotificationComponent } from './shared/components/notification/notification.component';

@Component({
  selector: 'app-root',
  imports: [
    CommonModule,
    RouterOutlet,
    HeaderComponent,
    FooterComponent,
    NotificationComponent
  ],
  template: `
    <div class="app-container">
      <app-header />
      <main class="main-content">
        <router-outlet />
      </main>
      <app-footer />
      <app-notification />
    </div>
  `,
  styleUrl: './app.scss'
})
export class App implements OnInit {
  protected title = 'Travel Booking App';

  private readonly stateService = inject(StateService);
  private readonly authService = inject(AuthService);
  private readonly notificationService = inject(NotificationService);

  ngOnInit(): void {
    // Initialize application state
    this.stateService.initialize();

    // Set current user from auth service
    const currentUser = this.authService.currentUser();
    if (currentUser) {
      this.stateService.setCurrentUser(currentUser);
    }
  }
}
