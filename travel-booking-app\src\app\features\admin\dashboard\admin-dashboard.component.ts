import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AdminService, AuthService } from '../../../core/services';

interface DashboardStats {
  totalUsers: number;
  totalBookings: number;
  totalRevenue: number;
  totalTrips: number;
  pendingBookings: number;
  activeUsers: number;
  popularDestinations: Array<{name: string; bookings: number}>;
  recentBookings: Array<any>;
  monthlyRevenue: Array<{month: string; revenue: number}>;
  bookingsByStatus: Array<{status: string; count: number}>;
}

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="admin-dashboard-container">
      <div class="container">
        <div class="admin-header">
          <h1>Admin Dashboard</h1>
          <p>Manage your travel booking platform</p>
        </div>

        <div class="admin-grid">
          <div class="admin-card">
            <div class="card-icon">📊</div>
            <h3>Analytics</h3>
            <p>View platform statistics and insights</p>
            <div class="stats">
              <div class="stat">
                <span class="stat-number">1,234</span>
                <span class="stat-label">Total Users</span>
              </div>
              <div class="stat">
                <span class="stat-number">567</span>
                <span class="stat-label">Total Bookings</span>
              </div>
            </div>
          </div>

          <div class="admin-card">
            <div class="card-icon">🎒</div>
            <h3>Trip Management</h3>
            <p>Create and manage travel packages</p>
            <a routerLink="/admin/trips" class="btn btn-primary">Manage Trips</a>
          </div>

          <div class="admin-card">
            <div class="card-icon">📝</div>
            <h3>Blog Management</h3>
            <p>Create and publish travel stories</p>
            <a routerLink="/admin/blogs" class="btn btn-primary">Manage Blogs</a>
          </div>

          <div class="admin-card">
            <div class="card-icon">👥</div>
            <h3>User Management</h3>
            <p>Manage user accounts and permissions</p>
            <a routerLink="/admin/users" class="btn btn-primary">Manage Users</a>
          </div>

          <div class="admin-card">
            <div class="card-icon">📋</div>
            <h3>Booking Management</h3>
            <p>View and manage all bookings</p>
            <a routerLink="/admin/bookings" class="btn btn-primary">Manage Bookings</a>
          </div>

          <div class="admin-card">
            <div class="card-icon">⚙️</div>
            <h3>Settings</h3>
            <p>Configure platform settings</p>
            <button class="btn btn-outline">Coming Soon</button>
          </div>
        </div>

        <div class="quick-actions">
          <a routerLink="/dashboard" class="btn btn-outline">Back to User Dashboard</a>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .admin-dashboard-container {
      min-height: 100vh;
      background: #f1f5f9;
      padding: 40px 0;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    .admin-header {
      text-align: center;
      margin-bottom: 50px;
    }
    .admin-header h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 15px;
    }
    .admin-header p {
      color: #64748b;
      font-size: 1.1rem;
    }
    .admin-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 30px;
      margin-bottom: 50px;
    }
    .admin-card {
      background: white;
      padding: 30px;
      border-radius: 15px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      text-align: center;
      transition: transform 0.3s ease;
      border: 1px solid #e2e8f0;
    }
    .admin-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    .card-icon {
      font-size: 3rem;
      margin-bottom: 20px;
    }
    .admin-card h3 {
      font-size: 1.3rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 10px;
    }
    .admin-card p {
      color: #64748b;
      margin-bottom: 20px;
    }
    .stats {
      display: flex;
      justify-content: space-around;
      margin-top: 20px;
    }
    .stat {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .stat-number {
      font-size: 1.5rem;
      font-weight: 700;
      color: #3b82f6;
    }
    .stat-label {
      font-size: 0.8rem;
      color: #64748b;
      margin-top: 5px;
    }
    .btn {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      text-decoration: none;
      display: inline-block;
      transition: all 0.3s ease;
      border: none;
      cursor: pointer;
    }
    .btn-primary {
      background: #3b82f6;
      color: white;
    }
    .btn-primary:hover {
      background: #2563eb;
    }
    .btn-outline {
      background: transparent;
      color: #3b82f6;
      border: 2px solid #3b82f6;
    }
    .btn-outline:hover {
      background: #3b82f6;
      color: white;
    }
    .quick-actions {
      text-align: center;
    }
  `]
})
export class AdminDashboardComponent {}
