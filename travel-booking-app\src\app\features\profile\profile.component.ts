import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="profile-container">
      <div class="container">
        <h1>Profile Settings</h1>
        <p>Profile management will be implemented here</p>
        <a routerLink="/dashboard" class="btn btn-primary">Back to Dashboard</a>
      </div>
    </div>
  `,
  styles: [`
    .profile-container {
      min-height: 100vh;
      background: #f8f9fa;
      padding: 40px 0;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 20px;
      background: white;
      border-radius: 15px;
      text-align: center;
    }
    h1 {
      font-size: 2rem;
      color: #2d3748;
      margin-bottom: 15px;
    }
    p {
      color: #718096;
      margin-bottom: 30px;
    }
    .btn {
      padding: 12px 24px;
      background: #667eea;
      color: white;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
    }
  `]
})
export class ProfileComponent {}
