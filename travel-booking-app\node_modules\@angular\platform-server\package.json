{"name": "@angular/platform-server", "version": "20.0.6", "description": "Angular - library for using Angular in Node.js", "author": "angular", "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0"}, "peerDependencies": {"@angular/common": "20.0.6", "@angular/compiler": "20.0.6", "@angular/core": "20.0.6", "@angular/platform-browser": "20.0.6", "rxjs": "^6.5.3 || ^7.4.0"}, "dependencies": {"tslib": "^2.3.0", "xhr2": "^0.2.0"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/platform-server"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": ["./fesm2022/init.mjs"], "module": "./fesm2022/platform-server.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/platform-server.mjs"}, "./init": {"types": "./init/index.d.ts", "default": "./fesm2022/init.mjs"}, "./testing": {"types": "./testing/index.d.ts", "default": "./fesm2022/testing.mjs"}}}