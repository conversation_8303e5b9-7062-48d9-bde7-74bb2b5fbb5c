import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'app-booking-detail',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="booking-detail-container">
      <div class="container">
        <h1>Booking Details</h1>
        <p>Booking detail view will be implemented here</p>
        <a routerLink="/bookings" class="btn btn-primary">Back to Bookings</a>
      </div>
    </div>
  `,
  styles: [`
    .booking-detail-container {
      min-height: 100vh;
      background: #f8f9fa;
      padding: 40px 0;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 20px;
      background: white;
      border-radius: 15px;
      text-align: center;
    }
    h1 {
      font-size: 2rem;
      color: #2d3748;
      margin-bottom: 15px;
    }
    p {
      color: #718096;
      margin-bottom: 30px;
    }
    .btn {
      padding: 12px 24px;
      background: #667eea;
      color: white;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
    }
  `]
})
export class BookingDetailComponent {}
